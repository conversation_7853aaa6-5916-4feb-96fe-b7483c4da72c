import os

def recover_python_code_from_null_corrupted_file(filepath):
    """
    Tenta recuperar o código Python de um arquivo que está corrompido
    com uma sequência de bytes nulos ('\x00') no início.
    """
    
    # 1. Abrir o arquivo em modo binário para leitura
    # 'rb' permite que o Python leia o arquivo como uma sequência de bytes,
    # ignorando a interpretação de caracteres e os problemas com bytes nulos.
    try:
        with open(filepath, 'rb') as f:
            content_bytes = f.read()
    except FileNotFoundError:
        print(f"Erro: O arquivo '{filepath}' não foi encontrado. Verifique o caminho e o nome.")
        return False
    except Exception as e:
        print(f"Ocorreu um erro inesperado ao ler o arquivo '{filepath}': {e}")
        return False

    original_size = len(content_bytes)
    print(f"Tamanho original do arquivo lido: {original_size} bytes.")

    # 2. Encontrar o índice do primeiro byte que NÃO é nulo
    # Isso nos permite pular toda a sequência de bytes nulos no início.
    first_non_null_byte_index = 0
    for i, byte_value in enumerate(content_bytes):
        if byte_value != 0x00:  # 0x00 é o valor hexadecimal para o byte nulo
            first_non_null_byte_index = i
            break
    
    # 3. Lidar com o caso onde o arquivo pode ser todo nulo ou quase todo nulo
    if first_non_null_byte_index == 0 and content_bytes.startswith(b'\x00'):
        # Se o loop terminou e o índice ainda é 0, e o arquivo começa com um null byte,
        # significa que ou o arquivo é todo nulo, ou tem muitos nulls no início
        # e o "non-null" real está bem à frente ou não existe.
        if original_size > 0 and content_bytes.count(b'\x00') == original_size:
            print("Parece que o arquivo está preenchido inteiramente com bytes nulos. Não há código para recuperar.")
            return False
        elif original_size > 0:
            print("Aviso: O arquivo começa com bytes nulos, mas nenhum byte não nulo foi encontrado rapidamente. Tentando recuperar mesmo assim.")
            # Continuar a lógica para fatiar, pois first_non_null_byte_index ainda é 0,
            # e o arquivo pode ser apenas alguns nulls seguidos de código.
            
    # 4. Extrair o conteúdo limpo (seu código Python)
    # Fatiamos os bytes a partir do primeiro byte não nulo encontrado.
    cleaned_content_bytes = content_bytes[first_non_null_byte_index:]
    cleaned_size = len(cleaned_content_bytes)

    if first_non_null_byte_index > 0:
        print(f"**Sucesso:** {first_non_null_byte_index} bytes nulos removidos do início do arquivo.")
        print(f"Tamanho do conteúdo recuperado: {cleaned_size} bytes.")
    elif cleaned_size == original_size:
         print("Nenhum byte nulo foi encontrado no início do arquivo. O arquivo pode não estar corrompido dessa forma.")
         return False
    else:
        print("Nenhum byte nulo removido do início. O arquivo pode ter sido corrompido de outra forma ou está vazio.")
        return False


    # 5. Decodificar os bytes limpos para uma string de texto (código Python)
    # 'errors=replace' é uma boa opção para lidar com caracteres inválidos que possam
    # ter sobrado, substituindo-os por um caractere de substituição.
    try:
        decoded_content = cleaned_content_bytes.decode('utf-8', errors='replace')
    except UnicodeDecodeError:
        print("Aviso: Não foi possível decodificar o conteúdo para UTF-8 completamente. Tentando Latin-1.")
        # Se UTF-8 falhar, Latin-1 é mais permissiva e pode funcionar.
        decoded_content = cleaned_content_bytes.decode('latin-1', errors='replace')
    except Exception as e:
        print(f"Erro ao decodificar o conteúdo recuperado: {e}")
        return False

    # 6. Salvar o código Python recuperado em um novo arquivo
    # Isso evita sobrescrever o arquivo original corrompido.
    output_filepath = filepath.replace('.py', '_recovered.py')
    if output_filepath == filepath: # Se o nome não terminar com .py, adicione _recovered
        output_filepath = filepath + '_recovered'

    try:
        with open(output_filepath, 'w', encoding='utf-8') as f:
            f.write(decoded_content)
        print(f"\nSeu código Python recuperado foi salvo em: '{output_filepath}'")
        print("Você pode abrir este arquivo e verificar se o código está correto.")
        return True
    except Exception as e:
        print(f"Erro ao salvar o arquivo recuperado '{output_filepath}': {e}")
        return False

# --- Como usar o script ---

# 1. Salve o código acima como um arquivo .py (por exemplo, 'recover_script.py').
# 2. **Crie uma cópia do seu arquivo corrompido.** É sempre seguro trabalhar com cópias.
# 3. Chame a função `recover_python_code_from_null_corrupted_file` passando o caminho
#    completo para o seu arquivo Python corrompido.

# Exemplo de uso:
# Suponha que seu arquivo corrompido esteja em 'C:/Users/<USER>/Documents/meu_codigo_corrompido.py'
# ou no mesmo diretório onde você salvou o script de recuperação.

# Substitua 'seu_arquivo_corrompido.py' pelo nome real do seu arquivo.
file_to_recover = 'run_nahimic_mastering.py' 

print(f"Iniciando a recuperação para o arquivo: {file_to_recover}")
success = recover_python_code_from_null_corrupted_file(file_to_recover)

if success:
    print("\nRecuperação concluída. Verifique o novo arquivo com '_recovered' no nome.")
else:
    print("\nRecuperação falhou ou o arquivo não estava corrompido da forma esperada.")