try:
    with open('run_nahimic_mastering.py', 'rb') as f:
        content = f.read()
    print(f"Conteúdo lido (bytes): {content[:50]}...") # Exibe os primeiros 50 bytes
    # Tente decodificar para texto, ignorando erros
    decoded_content = content.decode('utf-8', errors='ignore')
    print(f"Conteúdo decodificado (texto): {decoded_content[:500]}...") # Exibe os primeiros 500 caracteres
except FileNotFoundError:
    print("O arquivo não foi encontrado.")
except Exception as e:
    print(f"Ocorreu um erro ao ler o arquivo: {e}")