import sys

def recover_file(input_file, output_file, input_encoding='latin-1', output_encoding='utf-8'):
    try:
        with open(input_file, 'rb') as f_in:
            data = f_in.read()
        
        # Attempt to decode with the input encoding
        decoded_data = data.decode(input_encoding)
        
        # Encode to the output encoding
        encoded_data = decoded_data.encode(output_encoding)
        
        with open(output_file, 'wb') as f_out:
            f_out.write(encoded_data)
        print(f'Successfully recovered {input_file} to {output_file}')
    except Exception as e:
        print(f'Error recovering {input_file}: {e}')

if __name__ == '__main__':
    # You can change these to recover other files
    recover_file('regions.py', 'regions_recovered.py') 